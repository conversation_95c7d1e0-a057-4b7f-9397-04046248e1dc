#!/usr/bin/env node

/**
 * Quick script to check <PERSON><PERSON>'s Clerk ID
 */

const { clerkClient } = require('@clerk/clerk-sdk-node')
require('dotenv').config({ path: '.env.local' })

async function checkVisar() {
  try {
    console.log('🔍 Looking up <PERSON><PERSON> in Clerk...')
    
    const users = await clerkClient.users.getUserList({
      emailAddress: ['<EMAIL>'],
      limit: 1
    })
    
    if (users.data.length > 0) {
      const user = users.data[0]
      console.log('✅ Found Visar in Clerk:')
      console.log('   Clerk ID:', user.id)
      console.log('   Email:', user.emailAddresses[0]?.emailAddress)
      console.log('   Name:', `${user.firstName} ${user.lastName}`)
      console.log('   Created:', user.createdAt)
      
      // Compare with database ID
      const dbId = 'user_xrqot8ohm_1753955213224'
      console.log('\n🔍 Database comparison:')
      console.log('   Database ID:', dbId)
      console.log('   Clerk ID:   ', user.id)
      console.log('   Match:      ', dbId === user.id ? '✅ YES' : '❌ NO')
      
      if (dbId !== user.id) {
        console.log('\n⚠️  ID MISMATCH DETECTED!')
        console.log('   This could explain why Visar cannot see his team.')
        console.log('   The database has:', dbId)
        console.log('   But Clerk has:   ', user.id)
      } else {
        console.log('\n✅ IDs match perfectly!')
      }
      
    } else {
      console.log('❌ Visar not found in Clerk')
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

checkVisar()
